@font-face {
    font-family: 'Boldivia';
    src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
    font-style: normal;
    font-weight: normal;
}

#s {
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: -20%;
    color: #785a98;
    font-style: bold;
    font-size: 700px;
    font-family: 'Boldivia';
    /* Make sure it's loaded correctly */
    font-weight: 800;
    overflow-wrap: break-word;
    /* ✅ modern alternative to word-wrap */

}

.design2 {
    height: 300px;
    width: 300px;
    background-color: transparent;
    position: fixed;
    border: 70px solid #dbdbdb;
    border-radius: 50%;
    top: -10rem;
    left: -10rem;
}

.design3 {
    width: 100px;
    height: 50px;
    color: black;
    font-family: 'boldivia';
    font-size: 2rem;
    position: fixed;
    left: 2rem;
    top: 3rem;
    cursor: pointer;
}

.design4 {
    width: 100px;
    height: 50px;
    color: black;
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1.5rem;
    position: fixed;
    left: 13.5rem;
    top: 3rem;
    cursor: pointer;
}

hr {
    border: none;
    height: 2px;
    background-color: black;
    width: 100%;
    margin: 4px 0;
}

#hr-1 {
    width: 100px;
    height: 1.5px;
    position: fixed;
    left: 32.1rem;
    bottom: 30.65rem;
}

#hr-2 {
    width: 100px;
    height: 1.5px;
    position: fixed;
    background-color: rgb(185, 185, 185);
    left: 58.85rem;
    bottom: 30.65rem;
}

#hr-3 {
    width: 100px;
    position: fixed;
    left: 30.5rem;
    bottom: 11.6rem;
}

#hr-4 {
    width: 100px;
    background-color: rgb(185, 185, 185);
    position: fixed;
    left: 58.95rem;
    bottom: 11.6rem;
    ;
}

.design5 {
    height: auto;
    width: fit-content;
    color: black;
    font-style: normal;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: fixed;
    left: 28.5rem;
    top: 11.5rem;
}

.design6 {
    height: auto;
    width: fit-content;
    color: rgb(185, 185, 185);
    font-style: normal;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: fixed;
    left: 65.5rem;
    top: 11.5rem;
}

.design7 {
    height: auto;
    width: fit-content;
    color: black;
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: fixed;
    left: 26.9rem;
    top: 30.5rem;
}

.design8 {
    height: auto;
    width: fit-content;
    color: rgb(185, 185, 185);
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: fixed;
    left: 65.7rem;
    top: 30.5rem;
}

.design9 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 20rem;
    top: 16.5rem;

}

.design10 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 25.5rem;
    top: 16.5rem;

}

.design11 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 33.5rem;
    top: 16.5rem;

}

.design12 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 59rem;
    top: 16.5rem;

}

.design13 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 67rem;
    top: 16.5rem;

}

.design14 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: fixed;
    left: 74rem;
    top: 16.5rem;

}

.design15 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1.5rem;
    position: fixed;
    left: 65rem;
    top: 3rem;
    cursor: pointer;
}

.design16 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1.5rem;
    position: fixed;
    left: 71.5rem;
    top: 3rem;
    cursor: pointer;
}

.design17 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1.5rem;
    position: fixed;
    left: 85rem;
    top: 3rem;
    cursor: pointer;

}
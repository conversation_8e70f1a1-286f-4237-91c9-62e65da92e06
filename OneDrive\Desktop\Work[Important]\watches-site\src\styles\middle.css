/* Middle Section Styles */
.middle-section {
  width: 100%;
  padding: 60px 20px 40px;
  background: transparent;
  position: relative;
  z-index: 1;
}

/* 6-Box Image Grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto 60px;
  padding: 0 20px;
}

/* Individual Image Box */
.image-box {
  aspect-ratio: 4/3;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.image-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Image Placeholder */
.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Different gradient backgrounds for each box */
.image-box:nth-child(1) .image-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.image-box:nth-child(2) .image-placeholder {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.image-box:nth-child(3) .image-placeholder {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.image-box:nth-child(4) .image-placeholder {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.image-box:nth-child(5) .image-placeholder {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.image-box:nth-child(6) .image-placeholder {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Horizontal Scrolling Message Container */
.scrolling-message-container {
  width: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  padding: 15px 0;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  max-width: 1200px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Scrolling Message */
.scrolling-message {
  display: flex;
  white-space: nowrap;
  animation: scroll-horizontal 30s linear infinite;
}

.scrolling-message span {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Scrolling Animation */
@keyframes scroll-horizontal {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 15px;
    padding: 0 10px;
  }

  .middle-section {
    padding: 40px 10px 30px;
  }

  .scrolling-message span {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 1fr);
    gap: 10px;
  }

  .image-placeholder {
    font-size: 1rem;
  }
}

.Btn {
  width: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: transparent;
  position: fixed;
  /* overflow: hidden; */
  border-radius: 7px;
  cursor: pointer;
  transition: all 0.3s;
  top: 39rem;
  left: 80rem;
}

.svgContainer {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  backdrop-filter: blur(0px);
  letter-spacing: 0.8px;
  border-radius: 10px;
  transition: all 0.3s;
  border: 0px solid rgba(156, 156, 156, 0);
  z-index: 3;
}

.BG {
  position: absolute;
  content: "";
  width: 45px;
  height: 45px;
  background: #e60023;
  z-index: 2;
  border-radius: 10px;
  pointer-events: none;
  transition: all 0.3s;
}

.Btn:hover .BG {
  transform: rotate(35deg);
  transform-origin: bottom;
}

.Btn:hover .svgContainer {
  background-color: rgba(226, 226, 226, 0.27);
  backdrop-filter: blur(4px);
}

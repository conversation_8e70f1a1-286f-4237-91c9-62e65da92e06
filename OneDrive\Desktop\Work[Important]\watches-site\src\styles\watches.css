/* Carousel Container */
.carousel-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* Main Carousel Section */
.carousel-main {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Image Container */
.carousel-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: transparent;
}

/* Main Image */
.carousel-main-image {
  width: 250px;
  height: 250px;
  object-fit: contain;
  transition: opacity 0.3s ease-in-out;
}

/* Fade Effects */
.carousel-main-image.fade-in {
  opacity: 1;
}

.carousel-main-image.fade-out {
  opacity: 0;
}

/* Arrow Buttons */
.carousel-arrow {
  position: absolute;
  bottom: -7rem;
  left: -33rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 50px;
  background: transparent;
  border:  solid #000000;
  border-radius: 0px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #000000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 20;
}

.carousel-arrow-right {
  left: 80px;
  margin-left: -33.5rem;
}

.carousel-arrow:hover {
  background: transparent;
  border-color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.carousel-arrow:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.carousel-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.carousel-arrow:disabled:hover {
  background: #ffffff;
  border-color: #e0e0e0;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Arrow SVG Icons */
.carousel-arrow svg {
  width: 24px;
  height: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .carousel-main {
    gap: 10px;
  }

  .carousel-arrow {
    width: 40px;
    height: 40px;
  }

  .carousel-arrow svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .carousel-container {
    padding: 10px;
  }

  .carousel-main {
    flex-direction: column;
    gap: 15px;
  }

  .carousel-arrow {
    position: absolute;
    z-index: 10;
  }

  .carousel-arrow-left {
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .carousel-arrow-right {
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .carousel-arrow:hover {
    transform: translateY(-50%) scale(1.1);
  }
}
import { useState } from 'react'
import '../styles/middle.css'

function Middle() {
  const [count, setCount] = useState(0);

  return (
    <section className="middle-section">
      {/* 6-Box Image Grid */}
      <div className="image-grid">
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 1</span>
          </div>
        </div>
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 2</span>
          </div>
        </div>
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 3</span>
          </div>
        </div>
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 4</span>
          </div>
        </div>
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 5</span>
          </div>
        </div>
        <div className="image-box">
          <div className="image-placeholder">
            <span>Image 6</span>
          </div>
        </div>
      </div>

      {/* Horizontal Scrolling Message Line */}
      <div className="scrolling-message-container">
        <div className="scrolling-message">
          <span>🕐 Premium Watches Collection • Luxury Timepieces • Swiss Made • Limited Edition • Exclusive Designs • Crafted with Precision • Timeless Elegance • 🕐 Premium Watches Collection • Luxury Timepieces • Swiss Made • Limited Edition • Exclusive Designs • Crafted with Precision • Timeless Elegance • </span>
        </div>
      </div>
    </section>
  );
}

export default Middle;
